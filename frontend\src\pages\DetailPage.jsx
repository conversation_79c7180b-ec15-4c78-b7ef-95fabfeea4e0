import { useState, useEffect } from 'react'
import { useParams, useLocation, useNavigate } from 'react-router-dom'
import InsightPanel from '../components/InsightPanel'
import PersonaTag from '../components/PersonaTag'
import { ArrowLeft, ExternalLink, Bookmark, Share2 } from 'lucide-react'
import { api } from '../services/api'

const DetailPage = () => {
  const { id } = useParams()
  const location = useLocation()
  const navigate = useNavigate()
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [userInsights, setUserInsights] = useState(null)

  const result = location.state?.result
  const query = location.state?.query

  // 根据真实数据生成用户洞察
  const generateUserInsights = (result) => {
    if (!result || !result._rawData) {
      return null
    }

    const { comment, commenterHistory, post, backendData } = result._rawData
    const karma = commenterHistory.comment_karma || commenterHistory.karma || 0
    const linkKarma = commenterHistory.link_karma || 0

    // 使用真实的历史评论数据
    const topComments = []

    // 添加当前评论
    topComments.push({
      snippet: comment.body.length > 100 ? comment.body.substring(0, 100) + '...' : comment.body,
      subreddit: post.subreddit || 'unknown',
      score: comment.score || 0
    })

    // 如果后端数据包含用户历史评论，使用真实数据
    if (backendData && backendData.user_histories) {
      const userHistory = backendData.user_histories.find(h => h.username === result.author)
      if (userHistory && userHistory.comments_data) {
        // 取前几个高分评论
        const sortedComments = userHistory.comments_data
          .filter(c => c.score > 5) // 只显示有一定分数的评论
          .sort((a, b) => b.score - a.score)
          .slice(0, 2) // 最多显示2个历史评论

        sortedComments.forEach(historyComment => {
          topComments.push({
            snippet: historyComment.body.length > 100 ?
              historyComment.body.substring(0, 100) + '...' :
              historyComment.body,
            subreddit: historyComment.subreddit || 'unknown',
            score: historyComment.score || 0
          })
        })
      }
    }

    // 尝试从LLM分析中获取用户深度分析
    let personalityAnalysis = ''
    let motivationAnalysis = ''
    let whyRecommended = ''

    // 检查是否有LLM分析数据
    if (backendData && backendData.llm_analysis && backendData.llm_analysis.motivation_analysis) {
      const userAnalysis = backendData.llm_analysis.motivation_analysis[result.author]
      if (userAnalysis && userAnalysis.length > 0) {
        const analysis = userAnalysis[0] // 取第一个分析结果

        // 使用LLM分析的专业背景作为性格分析
        personalityAnalysis = analysis.professional_background || ''

        // 使用LLM分析的参与动机
        motivationAnalysis = analysis.participation_motivation || ''

        // 使用LLM分析的用户画像作为推荐理由
        whyRecommended = analysis.user_profile || analysis.overall_assessment || ''
      }
    }

    // 如果没有LLM分析数据，回退到基于基础数据的分析
    if (!personalityAnalysis) {
      // 基于karma值分析用户活跃度
      if (karma > 10000) {
        personalityAnalysis += '资深Reddit用户，在社区中享有很高声誉。'
      } else if (karma > 5000) {
        personalityAnalysis += '活跃的社区成员，经常参与有价值的讨论。'
      } else if (karma > 1000) {
        personalityAnalysis += '有一定社区参与经验的用户。'
      } else {
        personalityAnalysis += '相对较新的用户。'
      }

      // 基于评论内容分析发言风格
      const commentText = comment.body?.toLowerCase() || ''
      const analysisKeywords = []

      if (commentText.includes('experience') || commentText.includes('i ') || commentText.includes('my ')) {
        analysisKeywords.push('个人经历导向')
      }
      if (commentText.includes('data') || commentText.includes('research') || commentText.includes('study')) {
        analysisKeywords.push('数据驱动')
      }
      if (commentText.includes('advice') || commentText.includes('suggest') || commentText.includes('recommend')) {
        analysisKeywords.push('乐于助人')
      }
      if (commentText.includes('think') || commentText.includes('believe') || commentText.includes('opinion')) {
        analysisKeywords.push('观点明确')
      }

      if (analysisKeywords.length > 0) {
        personalityAnalysis += ` 发言特点：${analysisKeywords.join('、')}。`
      }
    }

    if (!motivationAnalysis) {
      // 基于真实数据生成动机分析
      const motivationFactors = []

      if (comment.score > 20) {
        motivationFactors.push(`回答获得${comment.score}个赞，受到社区高度认可`)
      } else if (comment.score > 5) {
        motivationFactors.push(`回答获得${comment.score}个赞，得到一定认可`)
      }

    if (commentText.includes('advice') || commentText.includes('suggest') || commentText.includes('help')) {
      motivationFactors.push('主动提供建议和帮助')
    }

    if (commentText.includes('experience') || commentText.includes('i ') || commentText.includes('my ')) {
      motivationFactors.push('愿意分享个人经历')
    }

    if (comment.body.length > 200) {
      motivationFactors.push('提供详细深入的回答')
    }

    motivationAnalysis = motivationFactors.length > 0 ?
      `该用户表现出：${motivationFactors.join('、')}。` :
      '该用户积极参与社区讨论。'

    // 基于真实数据生成推荐理由
    const recommendationReasons = []

    if (karma > 5000) {
      recommendationReasons.push(`高karma值(${karma.toLocaleString()})显示良好声誉`)
    } else if (karma > 1000) {
      recommendationReasons.push(`karma值(${karma.toLocaleString()})表明有一定社区参与度`)
    }

    if (comment.score > 10) {
      recommendationReasons.push(`回答获得${comment.score}个赞`)
    }

    if (comment.body.length > 150) {
      recommendationReasons.push('回答内容详实')
    }

    if (analysisKeywords.includes('个人经历导向')) {
      recommendationReasons.push('基于真实经历提供建议')
    }

    const whyRecommended = recommendationReasons.length > 0 ?
      `推荐理由：${recommendationReasons.join('；')}。` :
      '该回答具有一定参考价值。'

    // 计算账户年龄（如果有数据）
    let accountAge = '未知'
    if (commenterHistory.account_created_utc) {
      const createdDate = new Date(commenterHistory.account_created_utc * 1000)
      const now = new Date()
      const diffYears = Math.floor((now - createdDate) / (1000 * 60 * 60 * 24 * 365))
      const diffMonths = Math.floor((now - createdDate) / (1000 * 60 * 60 * 24 * 30))

      if (diffYears > 0) {
        accountAge = `${diffYears}年`
      } else if (diffMonths > 0) {
        accountAge = `${diffMonths}个月`
      } else {
        accountAge = '不到1个月'
      }
    }

    return {
      username: result.author,
      karma: karma,
      linkKarma: linkKarma,
      accountAge: accountAge,
      topComments: topComments,
      personalityAnalysis: personalityAnalysis,
      motivationAnalysis: motivationAnalysis,
      whyRecommended: whyRecommended,
      // 添加一些统计信息
      stats: {
        commentLength: comment.body.length,
        commentScore: comment.score || 0,
        subreddit: post.subreddit || 'unknown',
        totalComments: commenterHistory.total_comments || 0,
        totalPosts: commenterHistory.total_posts || 0
      }
    }
  }

  useEffect(() => {
    if (!result || !query) {
      navigate('/')
      return
    }

    // 生成用户洞察
    const insights = generateUserInsights(result)
    setUserInsights(insights)
  }, [result, query, navigate])

  const handleBookmark = async () => {
    try {
      const newBookmarkState = !isBookmarked
      setIsBookmarked(newBookmarkState)

      if (newBookmarkState) {
        await api.addBookmark({
          id: result.id,
          query,
          result
        })
      } else {
        await api.removeBookmark(result.id)
      }
    } catch (error) {
      console.error('收藏操作失败:', error)
      // 恢复状态
      setIsBookmarked(!isBookmarked)
    }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `CogBridges - ${result.author}的回答`,
          text: result.summary,
          url: window.location.href
        })
      } catch (err) {
        console.log('分享取消')
      }
    } else {
      // 复制链接到剪贴板
      try {
        await navigator.clipboard.writeText(window.location.href)
        alert('链接已复制到剪贴板')
      } catch (err) {
        console.error('复制失败:', err)
      }
    }
  }

  if (!result) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">未找到相关数据</p>
          <button
            onClick={() => navigate('/')}
            className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            返回首页
          </button>
        </div>
      </div>
    )
  }

  // 获取可信度分数（基于karma和评分）
  const getCredibilityScore = () => {
    const karma = result.karma || 0
    const score = result.score || 0
    
    let credibility = 60 // 基础分数
    credibility += Math.min(karma / 200, 25) // karma贡献最多25分
    credibility += Math.min(score * 2, 15) // 评分贡献最多15分
    
    return Math.min(Math.round(credibility), 100)
  }

  const getRelevanceScore = () => {
    const queryWords = query.toLowerCase().split(' ')
    const commentWords = (result.comment || '').toLowerCase().split(' ')
    
    let relevance = 70 // 基础分数
    const matchCount = queryWords.filter(word => 
      commentWords.some(cword => cword.includes(word))
    ).length
    
    relevance += (matchCount / queryWords.length) * 30
    
    return Math.min(Math.round(relevance), 100)
  }

  const getUsefulnessScore = () => {
    const commentLength = (result.comment || '').length
    const score = result.score || 0
    
    let usefulness = 65 // 基础分数
    usefulness += Math.min(commentLength / 20, 20) // 长度贡献
    usefulness += Math.min(score, 15) // 评分贡献
    
    return Math.min(Math.round(usefulness), 100)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(-1)}
                className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="w-5 h-5" />
              </button>
              
              <div>
                <h1 className="text-lg font-semibold text-gray-800">
                  u/{result.author} 的回答分析
                </h1>
                <p className="text-sm text-gray-500">
                  关于 "{query}" 的深度洞察
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={handleShare}
                className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="分享"
              >
                <Share2 className="w-5 h-5" />
              </button>
              
              <button
                onClick={handleBookmark}
                className={`p-2 rounded-lg transition-colors ${
                  isBookmarked 
                    ? 'text-yellow-600 bg-yellow-50 hover:bg-yellow-100' 
                    : 'text-gray-400 hover:text-yellow-600 hover:bg-yellow-50'
                }`}
                title={isBookmarked ? '取消收藏' : '收藏'}
              >
                <Bookmark className={`w-5 h-5 ${isBookmarked ? 'fill-current' : ''}`} />
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-6 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧 - 原始回答内容 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 原问题上下文 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-800 mb-2">原问题</h3>
              <p className="text-blue-700">"{query}"</p>
            </div>

            {/* 回答内容 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-primary-600 font-semibold">
                      {result.author.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">u/{result.author}</h3>
                    <p className="text-sm text-gray-500">来自 r/{result.subreddit}</p>
                  </div>
                </div>
                
                <div className="text-right text-sm text-gray-500">
                  <div>{result.score} 赞</div>
                  <div>{result.karma} karma</div>
                </div>
              </div>

              {/* 标签 */}
              <div className="flex flex-wrap gap-2 mb-4">
                {result.tags?.map((tag, index) => (
                  <PersonaTag key={index} tag={tag} size="sm" />
                ))}
              </div>

              {/* 回答正文 */}
              <div className="prose prose-gray max-w-none">
                <p className="text-gray-700 leading-relaxed text-lg">
                  {result._rawData?.comment?.body || result.comment}
                </p>
              </div>

              {/* 推荐理由 */}
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">为什么推荐这个回答</h4>
                <p className="text-green-700 text-sm">
                  {result.recommendation}
                </p>
              </div>

              {/* 原帖链接 */}
              <div className="mt-4 pt-4 border-t border-gray-100">
                <a
                  href={result.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-primary-600 hover:text-primary-700 text-sm"
                >
                  <ExternalLink className="w-4 h-4 mr-1" />
                  查看原帖完整讨论
                </a>
              </div>
            </div>

            {/* 可信度评估 */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="font-semibold text-gray-800 mb-4">可信度评估</h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-2 rounded-full border-4 border-green-200 flex items-center justify-center">
                    <span className="text-2xl font-bold text-green-600">{getCredibilityScore()}%</span>
                  </div>
                  <div className="text-sm font-medium text-gray-700">整体可信度</div>
                  <div className="text-xs text-gray-500">基于karma和社区反馈</div>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-2 rounded-full border-4 border-blue-200 flex items-center justify-center">
                    <span className="text-2xl font-bold text-blue-600">{getRelevanceScore()}%</span>
                  </div>
                  <div className="text-sm font-medium text-gray-700">相关性</div>
                  <div className="text-xs text-gray-500">与问题匹配度</div>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-2 rounded-full border-4 border-purple-200 flex items-center justify-center">
                    <span className="text-2xl font-bold text-purple-600">{getUsefulnessScore()}%</span>
                  </div>
                  <div className="text-sm font-medium text-gray-700">实用性</div>
                  <div className="text-xs text-gray-500">可操作程度</div>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧 - 用户深度分析 */}
          <div className="lg:col-span-1">
            {userInsights && <InsightPanel userInsights={userInsights} />}
          </div>
        </div>
      </div>
    </div>
  )
}

export default DetailPage 